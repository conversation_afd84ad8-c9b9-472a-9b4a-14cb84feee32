use eframe::egui;
use std::sync::{Arc, Mutex};
use std::thread;
use crate::{
    get_cursor_version, close_cursor_process, get_storage_file_path, get_backup_dir_path,
    update_storage_file, update_machine_guid, new_standard_machine_id, get_random_hex
};
use uuid::Uuid;
use chrono::Local;
use std::fs;

#[derive(Default)]
pub struct CursorResetApp {
    log_messages: Arc<Mutex<Vec<String>>>,
    is_processing: Arc<Mutex<bool>>,
    progress: Arc<Mutex<f32>>,
    show_admin_warning: bool,
}

impl CursorResetApp {
    pub fn new(_cc: &eframe::CreationContext<'_>) -> Self {
        // Check admin privileges
        let is_admin = is_elevated::is_elevated();
        
        Self {
            log_messages: Arc::new(Mutex::new(Vec::new())),
            is_processing: Arc::new(Mutex::new(false)),
            progress: Arc::new(Mutex::new(0.0)),
            show_admin_warning: !is_admin,
        }
    }

    fn add_log(&self, message: String) {
        if let Ok(mut logs) = self.log_messages.lock() {
            logs.push(format!("[{}] {}", Local::now().format("%H:%M:%S"), message));
        }
    }

    fn reset_cursor_id(&self) {
        let log_messages = Arc::clone(&self.log_messages);
        let is_processing = Arc::clone(&self.is_processing);
        let progress = Arc::clone(&self.progress);

        thread::spawn(move || {
            // Set processing state
            if let Ok(mut processing) = is_processing.lock() {
                *processing = true;
            }

            // Helper function to add log messages
            let add_log = |msg: String| {
                if let Ok(mut logs) = log_messages.lock() {
                    logs.push(format!("[{}] {}", Local::now().format("%H:%M:%S"), msg));
                }
            };

            // Helper function to update progress
            let update_progress = |p: f32| {
                if let Ok(mut prog) = progress.lock() {
                    *prog = p;
                }
            };

            add_log("开始重置 Cursor ID...".to_string());
            update_progress(0.1);

            // Get Cursor version
            match get_cursor_version() {
                Some(version) => add_log(format!("检测到 Cursor 版本: v{}", version)),
                None => add_log("警告: 无法检测 Cursor 版本".to_string()),
            }
            update_progress(0.2);

            // Close Cursor processes
            add_log("检查并关闭 Cursor 进程...".to_string());
            close_cursor_process("Cursor");
            close_cursor_process("cursor");
            update_progress(0.3);

            // Get file paths
            let storage_file_path = match get_storage_file_path() {
                Some(path) => path,
                None => {
                    add_log("错误: 无法确定存储文件路径".to_string());
                    if let Ok(mut processing) = is_processing.lock() {
                        *processing = false;
                    }
                    return;
                }
            };

            let backup_dir_path = match get_backup_dir_path() {
                Some(path) => path,
                None => {
                    add_log("错误: 无法确定备份目录路径".to_string());
                    if let Ok(mut processing) = is_processing.lock() {
                        *processing = false;
                    }
                    return;
                }
            };
            update_progress(0.4);

            // Create backup directory
            if !backup_dir_path.exists() {
                match fs::create_dir_all(&backup_dir_path) {
                    Ok(_) => add_log(format!("创建备份目录: {:?}", backup_dir_path)),
                    Err(e) => {
                        add_log(format!("错误: 创建备份目录失败: {}", e));
                        if let Ok(mut processing) = is_processing.lock() {
                            *processing = false;
                        }
                        return;
                    }
                }
            }
            update_progress(0.5);

            // Backup existing configuration
            if storage_file_path.exists() {
                add_log("备份现有配置文件...".to_string());
                let backup_name = format!("storage.json.backup_{}", Local::now().format("%Y%m%d_%H%M%S"));
                let backup_file_path = backup_dir_path.join(backup_name);
                match fs::copy(&storage_file_path, &backup_file_path) {
                    Ok(_) => add_log(format!("配置文件已备份到: {:?}", backup_file_path)),
                    Err(e) => add_log(format!("警告: 备份配置文件失败: {}", e)),
                }
            } else {
                add_log("未找到现有配置文件，跳过备份".to_string());
            }
            update_progress(0.6);

            // Generate new IDs
            add_log("生成新的 ID...".to_string());
            let mac_machine_id = new_standard_machine_id();
            let uuid_str = Uuid::new_v4().to_string();
            let prefix_hex = "auth0|user_".as_bytes().iter().map(|b| format!("{:02x}", b)).collect::<String>();
            let random_part = get_random_hex(32);
            let machine_id = format!("{}{}", prefix_hex, random_part);
            let sqm_id = format!("{{{}}}", Uuid::new_v4().to_string().to_uppercase());
            update_progress(0.7);

            // Update MachineGuid in registry
            #[cfg(target_os = "windows")]
            {
                add_log("更新注册表 MachineGuid...".to_string());
                if update_machine_guid(&backup_dir_path) {
                    add_log("注册表更新成功".to_string());
                } else {
                    add_log("警告: 注册表更新失败".to_string());
                }
            }
            update_progress(0.8);

            // Update storage file
            add_log("更新配置文件...".to_string());
            if update_storage_file(&storage_file_path, &machine_id, &mac_machine_id, &uuid_str, &sqm_id) {
                add_log("配置文件更新成功".to_string());
                add_log(format!("machineId: {}", machine_id));
                add_log(format!("macMachineId: {}", mac_machine_id));
                add_log(format!("devDeviceId: {}", uuid_str));
                add_log(format!("sqmId: {}", sqm_id));
            } else {
                add_log("错误: 配置文件更新失败".to_string());
            }
            update_progress(0.9);

            add_log("Cursor ID 重置完成！请重启 Cursor 应用新配置。".to_string());
            update_progress(1.0);

            // Reset processing state
            if let Ok(mut processing) = is_processing.lock() {
                *processing = false;
            }
        });
    }
}

impl eframe::App for CursorResetApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        egui::CentralPanel::default().show(ctx, |ui| {
            // Title
            ui.heading("Cursor Device ID 重置工具");
            ui.separator();

            // Admin warning
            if self.show_admin_warning {
                ui.colored_label(egui::Color32::RED, "⚠️ 警告: 请以管理员身份运行此程序！");
                ui.separator();
            }

            // Cursor version info
            if let Some(version) = get_cursor_version() {
                ui.label(format!("当前 Cursor 版本: v{}", version));
            } else {
                ui.colored_label(egui::Color32::YELLOW, "无法检测 Cursor 版本");
            }
            ui.separator();

            // Progress bar
            if let Ok(progress) = self.progress.lock() {
                if *progress > 0.0 {
                    ui.label("进度:");
                    ui.add(egui::ProgressBar::new(*progress).show_percentage());
                    ui.separator();
                }
            }

            // Reset button
            let is_processing = self.is_processing.lock().map(|p| *p).unwrap_or(false);
            
            ui.horizontal(|ui| {
                if ui.add_enabled(!is_processing && !self.show_admin_warning, 
                    egui::Button::new("🔄 重置 Cursor ID")).clicked() {
                    self.reset_cursor_id();
                }
                
                if ui.button("🗑️ 清空日志").clicked() {
                    if let Ok(mut logs) = self.log_messages.lock() {
                        logs.clear();
                    }
                    if let Ok(mut progress) = self.progress.lock() {
                        *progress = 0.0;
                    }
                }
            });

            ui.separator();

            // Log display
            ui.label("操作日志:");
            egui::ScrollArea::vertical()
                .max_height(300.0)
                .show(ui, |ui| {
                    if let Ok(logs) = self.log_messages.lock() {
                        for log in logs.iter() {
                            if log.contains("错误") || log.contains("Error") {
                                ui.colored_label(egui::Color32::RED, log);
                            } else if log.contains("警告") || log.contains("Warning") {
                                ui.colored_label(egui::Color32::YELLOW, log);
                            } else if log.contains("成功") || log.contains("完成") {
                                ui.colored_label(egui::Color32::GREEN, log);
                            } else {
                                ui.label(log);
                            }
                        }
                    }
                });

            ui.separator();
            ui.small("Cursor ID Reset Tool - Community Edition");
            ui.small("QQ群: 951642519 (交流/下载纯免费自动账号切换工具)");
        });

        // Request repaint for real-time updates
        ctx.request_repaint();
    }
}
