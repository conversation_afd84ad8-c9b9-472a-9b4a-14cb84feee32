[package]
name = "cursor_reset_tool"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
uuid = { version = "1.6", features = ["v4", "serde"] }
colored = "2.0"
sysinfo = "0.30"
rand = "0.8"
chrono = "0.4"
is_elevated = "0.1.2" # For checking admin privileges
directories = "5.0" # For APPDATA, LOCALAPPDATA paths
eframe = "0.28" # For GUI
egui = "0.28" # For GUI
tokio = { version = "1.0", features = ["full"] } # For async operations

[target.'cfg(windows)'.dependencies]
winreg = "0.52"